[{"/Users/<USER>/Desktop/Link_CPQ/src/index.tsx": "1", "/Users/<USER>/Desktop/Link_CPQ/src/App.tsx": "2", "/Users/<USER>/Desktop/Link_CPQ/src/store/index.ts": "3", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/configurationSlice.ts": "4", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/authSlice.ts": "5", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/appSlice.ts": "6", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/analyticsSlice.ts": "7", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/productSlice.ts": "8", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/pricingSlice.ts": "9", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/bundleSlice.ts": "10", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/quoteSlice.ts": "11", "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/customerSlice.ts": "12", "/Users/<USER>/Desktop/Link_CPQ/src/pages/products/ProductListPage.tsx": "13", "/Users/<USER>/Desktop/Link_CPQ/src/pages/configurator/ConfiguratorPage.tsx": "14", "/Users/<USER>/Desktop/Link_CPQ/src/pages/products/ProductDetailPage.tsx": "15", "/Users/<USER>/Desktop/Link_CPQ/src/pages/quotes/QuoteListPage.tsx": "16", "/Users/<USER>/Desktop/Link_CPQ/src/pages/pricing/PricingPage.tsx": "17", "/Users/<USER>/Desktop/Link_CPQ/src/pages/dashboard/DashboardPage.tsx": "18", "/Users/<USER>/Desktop/Link_CPQ/src/pages/quotes/QuoteDetailPage.tsx": "19", "/Users/<USER>/Desktop/Link_CPQ/src/pages/analytics/AnalyticsPage.tsx": "20", "/Users/<USER>/Desktop/Link_CPQ/src/pages/users/UserManagementPage.tsx": "21", "/Users/<USER>/Desktop/Link_CPQ/src/pages/bundles/BundleListPage.tsx": "22", "/Users/<USER>/Desktop/Link_CPQ/src/pages/integration/IntegrationPage.tsx": "23", "/Users/<USER>/Desktop/Link_CPQ/src/pages/auth/LoginPage.tsx": "24", "/Users/<USER>/Desktop/Link_CPQ/src/pages/customer-portal/CustomerPortalPage.tsx": "25", "/Users/<USER>/Desktop/Link_CPQ/src/components/auth/ProtectedRoute.tsx": "26", "/Users/<USER>/Desktop/Link_CPQ/src/components/layout/AppLayout.tsx": "27", "/Users/<USER>/Desktop/Link_CPQ/src/services/api.ts": "28", "/Users/<USER>/Desktop/Link_CPQ/src/data/mockData.ts": "29"}, {"size": 737, "mtime": 1756710889520, "results": "30", "hashOfConfig": "31"}, {"size": 4352, "mtime": 1756710942524, "results": "32", "hashOfConfig": "31"}, {"size": 1392, "mtime": 1756710729169, "results": "33", "hashOfConfig": "31"}, {"size": 8013, "mtime": 1756710809372, "results": "34", "hashOfConfig": "31"}, {"size": 3043, "mtime": 1756711926739, "results": "35", "hashOfConfig": "31"}, {"size": 1680, "mtime": 1756710740234, "results": "36", "hashOfConfig": "31"}, {"size": 1426, "mtime": 1756773512873, "results": "37", "hashOfConfig": "31"}, {"size": 7204, "mtime": 1756712562295, "results": "38", "hashOfConfig": "31"}, {"size": 2231, "mtime": 1756710843637, "results": "39", "hashOfConfig": "31"}, {"size": 4367, "mtime": 1756710828742, "results": "40", "hashOfConfig": "31"}, {"size": 2369, "mtime": 1756773549424, "results": "41", "hashOfConfig": "31"}, {"size": 1600, "mtime": 1756710868561, "results": "42", "hashOfConfig": "31"}, {"size": 15848, "mtime": 1756712686665, "results": "43", "hashOfConfig": "31"}, {"size": 20083, "mtime": 1756712542993, "results": "44", "hashOfConfig": "31"}, {"size": 14706, "mtime": 1756712675458, "results": "45", "hashOfConfig": "31"}, {"size": 11394, "mtime": 1756711539322, "results": "46", "hashOfConfig": "31"}, {"size": 20495, "mtime": 1756732043374, "results": "47", "hashOfConfig": "31"}, {"size": 9902, "mtime": 1756731829130, "results": "48", "hashOfConfig": "31"}, {"size": 15974, "mtime": 1756712711336, "results": "49", "hashOfConfig": "31"}, {"size": 12235, "mtime": 1756712452761, "results": "50", "hashOfConfig": "31"}, {"size": 13398, "mtime": 1756711727782, "results": "51", "hashOfConfig": "31"}, {"size": 20379, "mtime": 1756711319330, "results": "52", "hashOfConfig": "31"}, {"size": 16898, "mtime": 1756712650697, "results": "53", "hashOfConfig": "31"}, {"size": 4587, "mtime": 1756711012632, "results": "54", "hashOfConfig": "31"}, {"size": 21504, "mtime": 1756712614664, "results": "55", "hashOfConfig": "31"}, {"size": 805, "mtime": 1756710991231, "results": "56", "hashOfConfig": "31"}, {"size": 5955, "mtime": 1756710979342, "results": "57", "hashOfConfig": "31"}, {"size": 11901, "mtime": 1756773476088, "results": "58", "hashOfConfig": "31"}, {"size": 8358, "mtime": 1756711858199, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4u5c16", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/Link_CPQ/src/index.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/App.tsx", ["147"], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/index.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/configurationSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/authSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/appSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/analyticsSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/productSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/pricingSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/bundleSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/quoteSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/store/slices/customerSlice.ts", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/products/ProductListPage.tsx", ["148", "149"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/configurator/ConfiguratorPage.tsx", ["150", "151", "152", "153", "154", "155", "156"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/products/ProductDetailPage.tsx", ["157", "158"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/quotes/QuoteListPage.tsx", ["159", "160"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/pricing/PricingPage.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/dashboard/DashboardPage.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/quotes/QuoteDetailPage.tsx", ["161", "162", "163", "164"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/analytics/AnalyticsPage.tsx", ["165", "166", "167"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/users/UserManagementPage.tsx", ["168", "169", "170"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/bundles/BundleListPage.tsx", ["171"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/integration/IntegrationPage.tsx", ["172", "173", "174", "175", "176", "177", "178"], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/auth/LoginPage.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/pages/customer-portal/CustomerPortalPage.tsx", ["179", "180", "181", "182", "183", "184", "185", "186", "187", "188"], [], "/Users/<USER>/Desktop/Link_CPQ/src/components/auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/components/layout/AppLayout.tsx", [], [], "/Users/<USER>/Desktop/Link_CPQ/src/services/api.ts", ["189", "190"], [], "/Users/<USER>/Desktop/Link_CPQ/src/data/mockData.ts", [], [], {"ruleId": "191", "severity": 1, "message": "192", "line": 26, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 26, "endColumn": 16}, {"ruleId": "191", "severity": 1, "message": "195", "line": 24, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 24, "endColumn": 17}, {"ruleId": "191", "severity": 1, "message": "196", "line": 43, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 43, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "197", "line": 1, "column": 27, "nodeType": "193", "messageId": "194", "endLine": 1, "endColumn": 36}, {"ruleId": "191", "severity": 1, "message": "198", "line": 13, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 13, "endColumn": 10}, {"ruleId": "191", "severity": 1, "message": "199", "line": 35, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 35, "endColumn": 28}, {"ruleId": "191", "severity": 1, "message": "200", "line": 42, "column": 26, "nodeType": "193", "messageId": "194", "endLine": 42, "endColumn": 40}, {"ruleId": "191", "severity": 1, "message": "201", "line": 47, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 47, "endColumn": 15}, {"ruleId": "191", "severity": 1, "message": "202", "line": 138, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 138, "endColumn": 20}, {"ruleId": "191", "severity": 1, "message": "203", "line": 139, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 139, "endColumn": 17}, {"ruleId": "191", "severity": 1, "message": "204", "line": 43, "column": 27, "nodeType": "193", "messageId": "194", "endLine": 43, "endColumn": 34}, {"ruleId": "191", "severity": 1, "message": "205", "line": 232, "column": 13, "nodeType": "193", "messageId": "194", "endLine": 232, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "206", "line": 18, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 18, "endColumn": 11}, {"ruleId": "191", "severity": 1, "message": "207", "line": 43, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 43, "endColumn": 17}, {"ruleId": "191", "severity": 1, "message": "197", "line": 1, "column": 17, "nodeType": "193", "messageId": "194", "endLine": 1, "endColumn": 26}, {"ruleId": "191", "severity": 1, "message": "208", "line": 20, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 20, "endColumn": 8}, {"ruleId": "191", "severity": 1, "message": "209", "line": 30, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 30, "endColumn": 22}, {"ruleId": "191", "severity": 1, "message": "205", "line": 208, "column": 13, "nodeType": "193", "messageId": "194", "endLine": 208, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "210", "line": 33, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 33, "endColumn": 11}, {"ruleId": "191", "severity": 1, "message": "211", "line": 48, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 48, "endColumn": 24}, {"ruleId": "191", "severity": 1, "message": "204", "line": 48, "column": 26, "nodeType": "193", "messageId": "194", "endLine": 48, "endColumn": 33}, {"ruleId": "191", "severity": 1, "message": "212", "line": 18, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 18, "endColumn": 9}, {"ruleId": "191", "severity": 1, "message": "213", "line": 38, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 38, "endColumn": 22}, {"ruleId": "191", "severity": 1, "message": "205", "line": 254, "column": 13, "nodeType": "193", "messageId": "194", "endLine": 254, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "214", "line": 44, "column": 11, "nodeType": "193", "messageId": "194", "endLine": 44, "endColumn": 18}, {"ruleId": "191", "severity": 1, "message": "215", "line": 26, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 26, "endColumn": 18}, {"ruleId": "191", "severity": 1, "message": "209", "line": 27, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 27, "endColumn": 22}, {"ruleId": "191", "severity": 1, "message": "199", "line": 28, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 28, "endColumn": 28}, {"ruleId": "191", "severity": 1, "message": "216", "line": 32, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 32, "endColumn": 17}, {"ruleId": "191", "severity": 1, "message": "217", "line": 34, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 34, "endColumn": 22}, {"ruleId": "191", "severity": 1, "message": "218", "line": 44, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 44, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "205", "line": 316, "column": 13, "nodeType": "193", "messageId": "194", "endLine": 316, "endColumn": 19}, {"ruleId": "191", "severity": 1, "message": "197", "line": 1, "column": 27, "nodeType": "193", "messageId": "194", "endLine": 1, "endColumn": 36}, {"ruleId": "191", "severity": 1, "message": "206", "line": 18, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 18, "endColumn": 11}, {"ruleId": "191", "severity": 1, "message": "219", "line": 30, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 30, "endColumn": 18}, {"ruleId": "191", "severity": 1, "message": "220", "line": 31, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 31, "endColumn": 15}, {"ruleId": "191", "severity": 1, "message": "195", "line": 32, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 32, "endColumn": 17}, {"ruleId": "191", "severity": 1, "message": "221", "line": 39, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 39, "endColumn": 15}, {"ruleId": "191", "severity": 1, "message": "215", "line": 42, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 42, "endColumn": 18}, {"ruleId": "191", "severity": 1, "message": "222", "line": 43, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 43, "endColumn": 15}, {"ruleId": "191", "severity": 1, "message": "223", "line": 73, "column": 10, "nodeType": "193", "messageId": "194", "endLine": 73, "endColumn": 25}, {"ruleId": "191", "severity": 1, "message": "224", "line": 216, "column": 9, "nodeType": "193", "messageId": "194", "endLine": 216, "endColumn": 32}, {"ruleId": "191", "severity": 1, "message": "225", "line": 10, "column": 3, "nodeType": "193", "messageId": "194", "endLine": 10, "endColumn": 30}, {"ruleId": "191", "severity": 1, "message": "226", "line": 230, "column": 7, "nodeType": "193", "messageId": "194", "endLine": 230, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'Content' is assigned a value but never used.", "Identifier", "unusedVar", "'SearchOutlined' is defined but never used.", "'products' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Tooltip' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'useAppSelector' is defined but never used.", "'Option' is assigned a value but never used.", "'productId' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'values' is assigned a value but never used.", "'Progress' is defined but never used.", "'quotes' is assigned a value but never used.", "'Alert' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'BarChart' is defined but never used.", "'dashboardData' is assigned a value but never used.", "'Switch' is defined but never used.", "'selectedUser' is assigned a value but never used.", "'bundles' is assigned a value but never used.", "'SettingOutlined' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'PauseCircleOutlined' is defined but never used.", "'modalMode' is assigned a value but never used.", "'MessageOutlined' is defined but never used.", "'StarOutlined' is defined but never used.", "'LikeOutlined' is defined but never used.", "'GiftOutlined' is defined but never used.", "'selectedProduct' is assigned a value but never used.", "'handleSaveConfiguration' is assigned a value but never used.", "'createMockPaginatedResponse' is defined but never used.", "'isDevelopment' is assigned a value but never used."]