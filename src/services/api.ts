import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, PaginationParams } from '@/types';
import {
  mockUsers,
  mockProducts,
  mockCustomers,
  mockQuotes,
  mockConfigurations,
  createMockApiResponse,
  createMockPaginatedResponse,
} from '@/data/mockData';

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api';

class ApiService {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('access_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // 通用请求方法
  async request<T = any>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.request<ApiResponse<T>>(config);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || '请求失败');
    }
  }

  // GET请求
  async get<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url,
      params,
    });
  }

  // POST请求
  async post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
    });
  }

  // PUT请求
  async put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
    });
  }

  // DELETE请求
  async delete<T = any>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url,
    });
  }

  // 分页查询
  async getPaginated<T = any>(url: string, params: PaginationParams): Promise<ApiResponse<T[]>> {
    return this.get<T[]>(url, params);
  }

  // 文件上传
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request<T>({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  // 文件下载
  async download(url: string, filename?: string): Promise<void> {
    try {
      const response = await this.instance.get(url, {
        responseType: 'blob',
      });

      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message || '下载失败');
    }
  }
}

// 创建API服务实例
export const apiService = new ApiService();

class MockApiService {
  // 模拟登录
  async login(credentials: { username: string; password: string }) {
    await mockDelay(1000);
    if (credentials.username === 'admin' && credentials.password === 'admin123') {
      return mockLoginResponse;
    }
    throw new Error('用户名或密码错误');
  }

  // 模拟获取当前用户
  async getCurrentUser() {
    await mockDelay(500);
    return createMockApiResponse(mockUsers[0]);
  }

  // 模拟产品API
  async getProducts(params: any) {
    await mockDelay(800);
    return createMockApiResponse(mockProducts);
  }

  async getProduct(id: string) {
    await mockDelay(500);
    const product = mockProducts.find(p => p.id === id);
    if (!product) throw new Error('产品未找到');
    return createMockApiResponse(product);
  }

  // 模拟客户API
  async getCustomers(params: any) {
    await mockDelay(600);
    return createMockApiResponse(mockCustomers);
  }

  // 模拟报价API
  async getQuotes(params: any) {
    await mockDelay(700);
    return createMockApiResponse(mockQuotes);
  }

  async shareQuote(id: string) {
    await mockDelay(500);
    const shareLink = `https://cpq.example.com/shared/${id}/${Date.now()}`;
    return createMockApiResponse(shareLink);
  }

  // 模拟配置API
  async getConfigurations(params: any) {
    await mockDelay(600);
    return createMockApiResponse(mockConfigurations);
  }

  // 模拟仪表板数据
  async getDashboardData() {
    await mockDelay(1000);
    return createMockApiResponse({
      totalQuotes: 1250,
      totalRevenue: 2500000,
      conversionRate: 0.35,
      averageQuoteValue: 2000,
      recentQuotes: mockQuotes.slice(0, 5),
      topProducts: mockProducts.slice(0, 5),
      salesTrend: [
        { month: '1月', sales: 65000, quotes: 120 },
        { month: '2月', sales: 78000, quotes: 145 },
        { month: '3月', sales: 92000, quotes: 168 },
      ],
    });
  }
}

// 根据环境选择API服务
const isDevelopment = process.env.NODE_ENV === 'development';
export const mockApiService = new MockApiService();

// 产品相关API
export const productApi = {
  // 产品分类
  getCategories: () => apiService.get('/products/categories'),
  createCategory: (data: any) => apiService.post('/products/categories', data),
  updateCategory: (id: string, data: any) => apiService.put(`/products/categories/${id}`, data),
  deleteCategory: (id: string) => apiService.delete(`/products/categories/${id}`),

  // 产品管理
  getProducts: (params: PaginationParams) => apiService.getPaginated('/products', params),
  getProduct: (id: string) => apiService.get(`/products/${id}`),
  createProduct: (data: any) => apiService.post('/products', data),
  updateProduct: (id: string, data: any) => apiService.put(`/products/${id}`, data),
  deleteProduct: (id: string) => apiService.delete(`/products/${id}`),
  uploadProductImage: (id: string, file: File) => apiService.upload(`/products/${id}/images`, file),
  getProductSpecifications: (id: string) => apiService.get(`/products/${id}/specifications`),
  updateProductSpecifications: (id: string, data: any) => apiService.put(`/products/${id}/specifications`, data),
};

// 配置相关API
export const configurationApi = {
  getConfigurations: (params: PaginationParams) => apiService.getPaginated('/configurations', params),
  getConfiguration: (id: string) => apiService.get(`/configurations/${id}`),
  createConfiguration: (data: any) => apiService.post('/configurations', data),
  updateConfiguration: (id: string, data: any) => apiService.put(`/configurations/${id}`, data),
  deleteConfiguration: (id: string) => apiService.delete(`/configurations/${id}`),
  validateConfiguration: (data: any) => apiService.post('/configurations/validate', data),
  getConfigurationTemplates: () => apiService.get('/configurations/templates'),
  createConfigurationTemplate: (data: any) => apiService.post('/configurations/templates', data),
  cloneConfiguration: (id: string) => apiService.post(`/configurations/${id}/clone`),
  getConfigurationPreview: (id: string) => apiService.get(`/configurations/${id}/preview`),

  // 智能配置
  smartBuild: (data: any) => apiService.post('/configurations/smart-build', data),
  aiRecommend: (data: any) => apiService.post('/configurations/ai-recommend', data),
  optimize: (data: any) => apiService.post('/configurations/optimize', data),
  simulate: (data: any) => apiService.post('/configurations/simulate', data),
  checkCompatibility: (data: any) => apiService.get('/configurations/compatibility', data),
  autoComplete: (data: any) => apiService.post('/configurations/auto-complete', data),
  getAlternatives: (id: string) => apiService.get(`/configurations/alternatives`, { configurationId: id }),
  costOptimize: (data: any) => apiService.post('/configurations/cost-optimize', data),
  performanceTest: (data: any) => apiService.post('/configurations/performance-test', data),
  get3DModel: (id: string) => apiService.get(`/configurations/${id}/3d-model`),

  // 高级配置场景
  scenarioBased: (data: any) => apiService.post('/configurations/scenario-based', data),
  multiStep: (data: any) => apiService.post('/configurations/multi-step', data),
  conditional: (data: any) => apiService.post('/configurations/conditional', data),
  getDependencies: (id: string) => apiService.get(`/configurations/dependencies`, { configurationId: id }),
  validateConstraints: (data: any) => apiService.post('/configurations/constraints', data),
  bulkCreate: (data: any) => apiService.post('/configurations/bulk-create', data),
  clone: (data: any) => apiService.post('/configurations/clone', data),
  migrate: (data: any) => apiService.post('/configurations/migrate', data),
  getAuditTrail: (id: string) => apiService.get(`/configurations/audit-trail`, { configurationId: id }),
  rollback: (data: any) => apiService.post('/configurations/rollback', data),
  impactAnalysis: (data: any) => apiService.get('/configurations/impact-analysis', data),
  merge: (data: any) => apiService.post('/configurations/merge', data),
};

// 配置规则API
export const configurationRuleApi = {
  getRules: (params: PaginationParams) => apiService.getPaginated('/configuration-rules', params),
  getRule: (id: string) => apiService.get(`/configuration-rules/${id}`),
  createRule: (data: any) => apiService.post('/configuration-rules', data),
  updateRule: (id: string, data: any) => apiService.put(`/configuration-rules/${id}`, data),
  deleteRule: (id: string) => apiService.delete(`/configuration-rules/${id}`),
  validateRule: (data: any) => apiService.post('/configuration-rules/validate', data),
  testRule: (data: any) => apiService.post('/configuration-rules/test', data),
  getConflicts: () => apiService.get('/configuration-rules/conflicts'),
};

// 套餐组合API
export const bundleApi = {
  getBundles: (params: PaginationParams) => apiService.getPaginated('/bundles', params),
  getBundle: (id: string) => apiService.get(`/bundles/${id}`),
  createBundle: (data: any) => apiService.post('/bundles', data),
  updateBundle: (id: string, data: any) => apiService.put(`/bundles/${id}`, data),
  deleteBundle: (id: string) => apiService.delete(`/bundles/${id}`),
  designBundle: (data: any) => apiService.post('/bundles/design', data),
  validateBundle: (data: any) => apiService.post('/bundles/validate', data),
  getBundleComponents: (id: string) => apiService.get(`/bundles/${id}/components`),
  getCrossSellRecommendations: (data: any) => apiService.post('/bundles/cross-sell', data),
  getUpSellRecommendations: (data: any) => apiService.post('/bundles/up-sell', data),
  checkCompatibility: (data: any) => apiService.get('/bundles/compatibility', data),
  optimizeBundle: (data: any) => apiService.post('/bundles/optimize', data),
  getBundleAnalytics: (id: string) => apiService.get(`/bundles/analytics`, { bundleId: id }),
};

export default apiService;
